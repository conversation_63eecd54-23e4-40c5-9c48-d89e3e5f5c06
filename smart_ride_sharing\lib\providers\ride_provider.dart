import 'package:flutter/material.dart';
import '../models/ride_model.dart';
import '../models/driver_model.dart';
import '../models/location_model.dart';

class RideProvider with ChangeNotifier {
  List<RideModel> _availableRides = [];
  List<RideModel> _rideHistory = [];
  RideModel? _currentRide;
  List<DriverModel> _nearbyDrivers = [];
  bool _isSearchingRide = false;
  bool _isLoading = false;
  String _selectedRideType = 'individual';

  // Getters
  List<RideModel> get availableRides => _availableRides;
  List<RideModel> get rideHistory => _rideHistory;
  RideModel? get currentRide => _currentRide;
  List<DriverModel> get nearbyDrivers => _nearbyDrivers;
  bool get isSearchingRide => _isSearchingRide;
  bool get isLoading => _isLoading;
  String get selectedRideType => _selectedRideType;

  RideProvider() {
    _initializeMockData();
  }

  void _initializeMockData() {
    // Mock ride history
    _rideHistory = [
      RideModel(
        id: '1',
        pickupLocation: LocationModel(
          address: '123 Main St, Downtown',
          latitude: 40.7128,
          longitude: -74.0060,
        ),
        dropoffLocation: LocationModel(
          address: '456 Oak Ave, Uptown',
          latitude: 40.7589,
          longitude: -73.9851,
        ),
        driver: DriverModel(
          id: '1',
          name: 'Mike Johnson',
          rating: 4.8,
          photoUrl: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150',
          vehicleModel: 'Toyota Camry',
          licensePlate: 'ABC-123',
          phoneNumber: '+**********',
        ),
        fare: 15.50,
        distance: 5.2,
        duration: 18,
        status: RideStatus.completed,
        rideType: RideType.individual,
        createdAt: DateTime.now().subtract(Duration(days: 2)),
        completedAt: DateTime.now().subtract(Duration(days: 2, hours: -1)),
      ),
      RideModel(
        id: '2',
        pickupLocation: LocationModel(
          address: '789 Pine St, Midtown',
          latitude: 40.7505,
          longitude: -73.9934,
        ),
        dropoffLocation: LocationModel(
          address: '321 Elm St, Downtown',
          latitude: 40.7128,
          longitude: -74.0060,
        ),
        driver: DriverModel(
          id: '2',
          name: 'Sarah Wilson',
          rating: 4.9,
          photoUrl: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150',
          vehicleModel: 'Honda Accord',
          licensePlate: 'XYZ-789',
          phoneNumber: '+**********',
        ),
        fare: 22.75,
        distance: 7.8,
        duration: 25,
        status: RideStatus.completed,
        rideType: RideType.shared,
        sharedRiders: ['John Doe', 'Jane Smith'],
        createdAt: DateTime.now().subtract(Duration(days: 5)),
        completedAt: DateTime.now().subtract(Duration(days: 5, hours: -1)),
      ),
    ];

    // Mock nearby drivers
    _nearbyDrivers = [
      DriverModel(
        id: '3',
        name: 'Alex Chen',
        rating: 4.7,
        photoUrl: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150',
        vehicleModel: 'Nissan Altima',
        licensePlate: 'DEF-456',
        phoneNumber: '+**********',
        currentLocation: LocationModel(
          address: 'Near you',
          latitude: 40.7300,
          longitude: -74.0000,
        ),
        estimatedArrival: 3,
      ),
      DriverModel(
        id: '4',
        name: 'Maria Garcia',
        rating: 4.9,
        photoUrl: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150',
        vehicleModel: 'Hyundai Elantra',
        licensePlate: 'GHI-789',
        phoneNumber: '+**********',
        currentLocation: LocationModel(
          address: 'Near you',
          latitude: 40.7250,
          longitude: -74.0100,
        ),
        estimatedArrival: 5,
      ),
    ];
  }

  void setRideType(String rideType) {
    _selectedRideType = rideType;
    notifyListeners();
  }

  Future<void> searchForRide(LocationModel pickup, LocationModel dropoff) async {
    _isSearchingRide = true;
    _isLoading = true;
    notifyListeners();

    try {
      // Simulate AI-powered ride matching
      await Future.delayed(Duration(seconds: 3));

      if (_selectedRideType == 'shared') {
        // AI Smart Matching Logic
        await _findSharedRideMatches(pickup, dropoff);
      } else {
        // Individual ride
        await _findIndividualRide(pickup, dropoff);
      }
    } catch (e) {
      print('Error searching for ride: $e');
    }

    _isSearchingRide = false;
    _isLoading = false;
    notifyListeners();
  }

  Future<void> _findSharedRideMatches(LocationModel pickup, LocationModel dropoff) async {
    // Simulate AI matching algorithm
    await Future.delayed(Duration(seconds: 2));

    // Check for existing rides that can accommodate new rider
    List<RideModel> compatibleRides = _availableRides.where((ride) {
      return ride.rideType == RideType.shared &&
             ride.status == RideStatus.searching &&
             _isRouteCompatible(ride, pickup, dropoff);
    }).toList();

    if (compatibleRides.isNotEmpty) {
      // Found compatible shared ride
      RideModel bestMatch = compatibleRides.first;
      _currentRide = bestMatch.copyWith(
        sharedRiders: [...(bestMatch.sharedRiders ?? []), 'You'],
      );
    } else {
      // Create new shared ride
      _currentRide = RideModel(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        pickupLocation: pickup,
        dropoffLocation: dropoff,
        driver: _nearbyDrivers.first,
        fare: _calculateSharedFare(pickup, dropoff),
        distance: _calculateDistance(pickup, dropoff),
        duration: _calculateDuration(pickup, dropoff),
        status: RideStatus.searching,
        rideType: RideType.shared,
        createdAt: DateTime.now(),
      );
    }
  }

  Future<void> _findIndividualRide(LocationModel pickup, LocationModel dropoff) async {
    _currentRide = RideModel(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      pickupLocation: pickup,
      dropoffLocation: dropoff,
      driver: _nearbyDrivers.first,
      fare: _calculateIndividualFare(pickup, dropoff),
      distance: _calculateDistance(pickup, dropoff),
      duration: _calculateDuration(pickup, dropoff),
      status: RideStatus.searching,
      rideType: RideType.individual,
      createdAt: DateTime.now(),
    );
  }

  bool _isRouteCompatible(RideModel existingRide, LocationModel pickup, LocationModel dropoff) {
    // AI logic to determine route compatibility
    // This is a simplified version - in real app, this would use complex algorithms
    double pickupDistance = _calculateDistanceBetweenPoints(
      existingRide.pickupLocation, pickup
    );
    double dropoffDistance = _calculateDistanceBetweenPoints(
      existingRide.dropoffLocation, dropoff
    );
    
    // Compatible if pickup and dropoff are within 2km of existing route
    return pickupDistance <= 2.0 && dropoffDistance <= 2.0;
  }

  double _calculateDistance(LocationModel pickup, LocationModel dropoff) {
    // Simplified distance calculation
    return _calculateDistanceBetweenPoints(pickup, dropoff);
  }

  double _calculateDistanceBetweenPoints(LocationModel point1, LocationModel point2) {
    // Simplified distance calculation (in real app, use proper geo calculations)
    double latDiff = (point1.latitude - point2.latitude).abs();
    double lonDiff = (point1.longitude - point2.longitude).abs();
    return (latDiff + lonDiff) * 111; // Rough km conversion
  }

  int _calculateDuration(LocationModel pickup, LocationModel dropoff) {
    double distance = _calculateDistance(pickup, dropoff);
    return (distance / 0.5).round(); // Assume 30 km/h average speed
  }

  double _calculateIndividualFare(LocationModel pickup, LocationModel dropoff) {
    double distance = _calculateDistance(pickup, dropoff);
    return 5.0 + (distance * 2.5); // Base fare + distance rate
  }

  double _calculateSharedFare(LocationModel pickup, LocationModel dropoff) {
    double individualFare = _calculateIndividualFare(pickup, dropoff);
    return individualFare * 0.7; // 30% discount for shared rides
  }

  Future<void> confirmRide() async {
    if (_currentRide != null) {
      _currentRide = _currentRide!.copyWith(status: RideStatus.confirmed);
      notifyListeners();
      
      // Simulate driver assignment and arrival
      await Future.delayed(Duration(seconds: 2));
      _currentRide = _currentRide!.copyWith(status: RideStatus.driverAssigned);
      notifyListeners();
    }
  }

  Future<void> cancelRide() async {
    if (_currentRide != null) {
      _currentRide = _currentRide!.copyWith(status: RideStatus.cancelled);
      _rideHistory.insert(0, _currentRide!);
      _currentRide = null;
      notifyListeners();
    }
  }

  Future<void> completeRide() async {
    if (_currentRide != null) {
      _currentRide = _currentRide!.copyWith(
        status: RideStatus.completed,
        completedAt: DateTime.now(),
      );
      _rideHistory.insert(0, _currentRide!);
      _currentRide = null;
      notifyListeners();
    }
  }

  void clearCurrentRide() {
    _currentRide = null;
    notifyListeners();
  }
}
