import 'dart:async';
import 'dart:math';
import 'package:flutter/material.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import '../models/location_model.dart';
import '../models/ride_model.dart';

class MapsService {
  static const String _apiKey = 'YOUR_GOOGLE_MAPS_API_KEY'; // Replace with actual API key
  
  // Mock real-time tracking data
  static Timer? _trackingTimer;
  static StreamController<DriverLocation>? _locationController;
  
  /// Initialize real-time tracking for a ride
  static Stream<DriverLocation> startRealTimeTracking(RideModel ride) {
    _locationController = StreamController<DriverLocation>.broadcast();
    
    // Simulate real-time driver movement
    _simulateDriverMovement(ride);
    
    return _locationController!.stream;
  }

  /// Stop real-time tracking
  static void stopRealTimeTracking() {
    _trackingTimer?.cancel();
    _locationController?.close();
    _trackingTimer = null;
    _locationController = null;
  }

  /// Simulate driver movement for demo purposes
  static void _simulateDriverMovement(RideModel ride) {
    LatLng currentPosition = LatLng(
      ride.driver.currentLocation!.latitude,
      ride.driver.currentLocation!.longitude,
    );
    
    LatLng destination = LatLng(
      ride.pickupLocation.latitude,
      ride.pickupLocation.longitude,
    );
    
    // If ride is in progress, move towards dropoff
    if (ride.status == RideStatus.inProgress) {
      destination = LatLng(
        ride.dropoffLocation.latitude,
        ride.dropoffLocation.longitude,
      );
    }
    
    int updateCount = 0;
    const int totalUpdates = 60; // 1 minute of updates
    
    _trackingTimer = Timer.periodic(Duration(seconds: 1), (timer) {
      updateCount++;
      
      // Calculate interpolated position
      double progress = updateCount / totalUpdates;
      LatLng interpolatedPosition = _interpolatePosition(currentPosition, destination, progress);
      
      // Add some realistic movement variation
      interpolatedPosition = _addMovementVariation(interpolatedPosition);
      
      // Calculate speed and bearing
      double speed = _calculateSpeed(currentPosition, interpolatedPosition);
      double bearing = _calculateBearing(currentPosition, interpolatedPosition);
      
      DriverLocation location = DriverLocation(
        driverId: ride.driver.id,
        latitude: interpolatedPosition.latitude,
        longitude: interpolatedPosition.longitude,
        speed: speed,
        bearing: bearing,
        timestamp: DateTime.now(),
        accuracy: 5.0 + Random().nextDouble() * 5.0, // 5-10m accuracy
        isMoving: speed > 1.0,
      );
      
      _locationController?.add(location);
      currentPosition = interpolatedPosition;
      
      // Stop when destination is reached or after reasonable time
      if (progress >= 1.0 || updateCount >= totalUpdates * 2) {
        timer.cancel();
      }
    });
  }

  /// Interpolate position between two points
  static LatLng _interpolatePosition(LatLng start, LatLng end, double progress) {
    progress = progress.clamp(0.0, 1.0);
    
    double lat = start.latitude + (end.latitude - start.latitude) * progress;
    double lng = start.longitude + (end.longitude - start.longitude) * progress;
    
    return LatLng(lat, lng);
  }

  /// Add realistic movement variation
  static LatLng _addMovementVariation(LatLng position) {
    Random random = Random();
    double latVariation = (random.nextDouble() - 0.5) * 0.0001; // ~10m variation
    double lngVariation = (random.nextDouble() - 0.5) * 0.0001;
    
    return LatLng(
      position.latitude + latVariation,
      position.longitude + lngVariation,
    );
  }

  /// Calculate speed between two positions
  static double _calculateSpeed(LatLng pos1, LatLng pos2) {
    double distance = _calculateDistance(pos1, pos2);
    return distance * 3600; // km/h (assuming 1 second interval)
  }

  /// Calculate bearing between two positions
  static double _calculateBearing(LatLng pos1, LatLng pos2) {
    double lat1Rad = pos1.latitude * pi / 180;
    double lat2Rad = pos2.latitude * pi / 180;
    double deltaLngRad = (pos2.longitude - pos1.longitude) * pi / 180;
    
    double y = sin(deltaLngRad) * cos(lat2Rad);
    double x = cos(lat1Rad) * sin(lat2Rad) - sin(lat1Rad) * cos(lat2Rad) * cos(deltaLngRad);
    
    double bearingRad = atan2(y, x);
    return (bearingRad * 180 / pi + 360) % 360;
  }

  /// Calculate distance between two coordinates
  static double _calculateDistance(LatLng pos1, LatLng pos2) {
    const double earthRadius = 6371; // km
    
    double lat1Rad = pos1.latitude * pi / 180;
    double lat2Rad = pos2.latitude * pi / 180;
    double deltaLatRad = (pos2.latitude - pos1.latitude) * pi / 180;
    double deltaLngRad = (pos2.longitude - pos1.longitude) * pi / 180;
    
    double a = sin(deltaLatRad / 2) * sin(deltaLatRad / 2) +
        cos(lat1Rad) * cos(lat2Rad) *
        sin(deltaLngRad / 2) * sin(deltaLngRad / 2);
    double c = 2 * atan2(sqrt(a), sqrt(1 - a));
    
    return earthRadius * c;
  }

  /// Generate route polyline points
  static List<LatLng> generateRoutePolyline(List<LocationModel> waypoints) {
    List<LatLng> polylinePoints = [];
    
    for (int i = 0; i < waypoints.length - 1; i++) {
      LatLng start = LatLng(waypoints[i].latitude, waypoints[i].longitude);
      LatLng end = LatLng(waypoints[i + 1].latitude, waypoints[i + 1].longitude);
      
      // Generate intermediate points for smooth polyline
      List<LatLng> segmentPoints = _generateSegmentPoints(start, end, 10);
      polylinePoints.addAll(segmentPoints);
    }
    
    return polylinePoints;
  }

  /// Generate intermediate points between two coordinates
  static List<LatLng> _generateSegmentPoints(LatLng start, LatLng end, int numPoints) {
    List<LatLng> points = [];
    
    for (int i = 0; i <= numPoints; i++) {
      double progress = i / numPoints;
      LatLng point = _interpolatePosition(start, end, progress);
      points.add(point);
    }
    
    return points;
  }

  /// Create map markers for ride
  static Set<Marker> createRideMarkers(RideModel ride, {DriverLocation? driverLocation}) {
    Set<Marker> markers = {};
    
    // Pickup marker
    markers.add(Marker(
      markerId: MarkerId('pickup'),
      position: LatLng(ride.pickupLocation.latitude, ride.pickupLocation.longitude),
      icon: BitmapDescriptor.defaultMarkerWithHue(BitmapDescriptor.hueGreen),
      infoWindow: InfoWindow(
        title: 'Pickup Location',
        snippet: ride.pickupLocation.shortAddress,
      ),
    ));
    
    // Dropoff marker
    markers.add(Marker(
      markerId: MarkerId('dropoff'),
      position: LatLng(ride.dropoffLocation.latitude, ride.dropoffLocation.longitude),
      icon: BitmapDescriptor.defaultMarkerWithHue(BitmapDescriptor.hueRed),
      infoWindow: InfoWindow(
        title: 'Destination',
        snippet: ride.dropoffLocation.shortAddress,
      ),
    ));
    
    // Driver marker (real-time position)
    if (driverLocation != null) {
      markers.add(Marker(
        markerId: MarkerId('driver'),
        position: LatLng(driverLocation.latitude, driverLocation.longitude),
        icon: BitmapDescriptor.defaultMarkerWithHue(BitmapDescriptor.hueBlue),
        rotation: driverLocation.bearing,
        infoWindow: InfoWindow(
          title: ride.driver.name,
          snippet: '${ride.driver.vehicleInfo} • ${driverLocation.speed.toStringAsFixed(1)} km/h',
        ),
      ));
    }
    
    // Shared riders markers (if applicable)
    if (ride.isSharedRide && ride.sharedRiders != null) {
      for (int i = 0; i < ride.sharedRiders!.length; i++) {
        // In a real app, you would have actual locations for shared riders
        markers.add(Marker(
          markerId: MarkerId('shared_rider_$i'),
          position: LatLng(
            ride.pickupLocation.latitude + (i * 0.001),
            ride.pickupLocation.longitude + (i * 0.001),
          ),
          icon: BitmapDescriptor.defaultMarkerWithHue(BitmapDescriptor.hueOrange),
          infoWindow: InfoWindow(
            title: 'Shared Rider',
            snippet: ride.sharedRiders![i],
          ),
        ));
      }
    }
    
    return markers;
  }

  /// Create polylines for route
  static Set<Polyline> createRoutePolylines(RideModel ride) {
    Set<Polyline> polylines = {};
    
    List<LatLng> routePoints = generateRoutePolyline([
      ride.pickupLocation,
      ride.dropoffLocation,
    ]);
    
    polylines.add(Polyline(
      polylineId: PolylineId('main_route'),
      points: routePoints,
      color: Colors.blue,
      width: 5,
      patterns: [],
    ));
    
    return polylines;
  }

  /// Calculate optimal camera position for ride
  static CameraPosition calculateOptimalCameraPosition(RideModel ride, {DriverLocation? driverLocation}) {
    List<LatLng> points = [
      LatLng(ride.pickupLocation.latitude, ride.pickupLocation.longitude),
      LatLng(ride.dropoffLocation.latitude, ride.dropoffLocation.longitude),
    ];
    
    if (driverLocation != null) {
      points.add(LatLng(driverLocation.latitude, driverLocation.longitude));
    }
    
    LatLngBounds bounds = _calculateBounds(points);
    LatLng center = LatLng(
      (bounds.northeast.latitude + bounds.southwest.latitude) / 2,
      (bounds.northeast.longitude + bounds.southwest.longitude) / 2,
    );
    
    return CameraPosition(
      target: center,
      zoom: _calculateZoomLevel(bounds),
      bearing: 0,
      tilt: 0,
    );
  }

  /// Calculate bounds for multiple points
  static LatLngBounds _calculateBounds(List<LatLng> points) {
    double minLat = points.first.latitude;
    double maxLat = points.first.latitude;
    double minLng = points.first.longitude;
    double maxLng = points.first.longitude;
    
    for (LatLng point in points) {
      minLat = min(minLat, point.latitude);
      maxLat = max(maxLat, point.latitude);
      minLng = min(minLng, point.longitude);
      maxLng = max(maxLng, point.longitude);
    }
    
    return LatLngBounds(
      southwest: LatLng(minLat, minLng),
      northeast: LatLng(maxLat, maxLng),
    );
  }

  /// Calculate appropriate zoom level
  static double _calculateZoomLevel(LatLngBounds bounds) {
    double latDiff = bounds.northeast.latitude - bounds.southwest.latitude;
    double lngDiff = bounds.northeast.longitude - bounds.southwest.longitude;
    double maxDiff = max(latDiff, lngDiff);
    
    if (maxDiff > 0.1) return 10.0;
    if (maxDiff > 0.05) return 12.0;
    if (maxDiff > 0.01) return 14.0;
    if (maxDiff > 0.005) return 15.0;
    return 16.0;
  }
}

class DriverLocation {
  final String driverId;
  final double latitude;
  final double longitude;
  final double speed; // km/h
  final double bearing; // degrees
  final DateTime timestamp;
  final double accuracy; // meters
  final bool isMoving;

  DriverLocation({
    required this.driverId,
    required this.latitude,
    required this.longitude,
    required this.speed,
    required this.bearing,
    required this.timestamp,
    required this.accuracy,
    required this.isMoving,
  });

  LatLng get position => LatLng(latitude, longitude);
  
  String get speedText => '${speed.toStringAsFixed(1)} km/h';
  String get accuracyText => '±${accuracy.toStringAsFixed(0)}m';
}
