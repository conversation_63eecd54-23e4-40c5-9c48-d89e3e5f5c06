import 'dart:async';
import 'package:flutter/material.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:provider/provider.dart';
import 'package:google_fonts/google_fonts.dart';
import '../../models/ride_model.dart';
import '../../services/maps_service.dart';
import '../../providers/ride_provider.dart';
import '../../utils/app_theme.dart';

class LiveTrackingScreen extends StatefulWidget {
  final RideModel ride;

  LiveTrackingScreen({required this.ride});

  @override
  _LiveTrackingScreenState createState() => _LiveTrackingScreenState();
}

class _LiveTrackingScreenState extends State<LiveTrackingScreen> with TickerProviderStateMixin {
  GoogleMapController? _mapController;
  StreamSubscription<DriverLocation>? _locationSubscription;
  DriverLocation? _currentDriverLocation;
  
  late AnimationController _pulseController;
  late Animation<double> _pulseAnimation;
  
  Set<Marker> _markers = {};
  Set<Polyline> _polylines = {};
  
  bool _isMapReady = false;
  bool _isFollowingDriver = true;

  @override
  void initState() {
    super.initState();
    
    _pulseController = AnimationController(
      duration: Duration(seconds: 2),
      vsync: this,
    );
    
    _pulseAnimation = Tween<double>(
      begin: 0.5,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _pulseController,
      curve: Curves.easeInOut,
    ));
    
    _pulseController.repeat(reverse: true);
    
    _initializeTracking();
  }

  @override
  void dispose() {
    _pulseController.dispose();
    _locationSubscription?.cancel();
    MapsService.stopRealTimeTracking();
    super.dispose();
  }

  void _initializeTracking() {
    // Start real-time tracking
    _locationSubscription = MapsService.startRealTimeTracking(widget.ride).listen(
      (DriverLocation location) {
        setState(() {
          _currentDriverLocation = location;
          _updateMapMarkers();
          
          if (_isFollowingDriver && _mapController != null) {
            _followDriver(location);
          }
        });
      },
    );
    
    // Initialize map markers and polylines
    _updateMapMarkers();
    _updateMapPolylines();
  }

  void _updateMapMarkers() {
    _markers = MapsService.createRideMarkers(widget.ride, driverLocation: _currentDriverLocation);
  }

  void _updateMapPolylines() {
    _polylines = MapsService.createRoutePolylines(widget.ride);
  }

  void _followDriver(DriverLocation location) {
    _mapController?.animateCamera(
      CameraUpdate.newCameraPosition(
        CameraPosition(
          target: LatLng(location.latitude, location.longitude),
          zoom: 16.0,
          bearing: location.bearing,
          tilt: 45.0,
        ),
      ),
    );
  }

  void _fitMapToRoute() {
    if (_mapController != null) {
      CameraPosition optimalPosition = MapsService.calculateOptimalCameraPosition(
        widget.ride,
        driverLocation: _currentDriverLocation,
      );
      
      _mapController!.animateCamera(
        CameraUpdate.newCameraPosition(optimalPosition),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      body: Stack(
        children: [
          // Google Map
          GoogleMap(
            onMapCreated: (GoogleMapController controller) {
              _mapController = controller;
              setState(() {
                _isMapReady = true;
              });
              
              // Set initial camera position
              Future.delayed(Duration(milliseconds: 500), () {
                _fitMapToRoute();
              });
            },
            initialCameraPosition: CameraPosition(
              target: LatLng(
                widget.ride.pickupLocation.latitude,
                widget.ride.pickupLocation.longitude,
              ),
              zoom: 14.0,
            ),
            markers: _markers,
            polylines: _polylines,
            myLocationEnabled: true,
            myLocationButtonEnabled: false,
            zoomControlsEnabled: false,
            mapToolbarEnabled: false,
            onTap: (_) {
              setState(() {
                _isFollowingDriver = false;
              });
            },
          ),
          
          // Top overlay with ride info
          _buildTopOverlay(),
          
          // Bottom overlay with driver info and controls
          _buildBottomOverlay(),
          
          // Floating action buttons
          _buildFloatingButtons(),
        ],
      ),
    );
  }

  Widget _buildTopOverlay() {
    return Positioned(
      top: 0,
      left: 0,
      right: 0,
      child: Container(
        padding: EdgeInsets.only(
          top: MediaQuery.of(context).padding.top + 10,
          left: 20,
          right: 20,
          bottom: 20,
        ),
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Colors.black.withOpacity(0.8),
              Colors.transparent,
            ],
          ),
        ),
        child: Column(
          children: [
            Row(
              children: [
                IconButton(
                  onPressed: () => Navigator.pop(context),
                  icon: Icon(Icons.arrow_back, color: Colors.white),
                ),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        widget.ride.statusText,
                        style: GoogleFonts.poppins(
                          color: Colors.white,
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Text(
                        'Trip ID: ${widget.ride.id.substring(0, 8)}',
                        style: GoogleFonts.poppins(
                          color: Colors.white.withOpacity(0.8),
                          fontSize: 12,
                        ),
                      ),
                    ],
                  ),
                ),
                Container(
                  padding: EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                  decoration: BoxDecoration(
                    color: AppTheme.primaryColor,
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      AnimatedBuilder(
                        animation: _pulseAnimation,
                        builder: (context, child) {
                          return Transform.scale(
                            scale: _pulseAnimation.value,
                            child: Container(
                              width: 8,
                              height: 8,
                              decoration: BoxDecoration(
                                color: Colors.white,
                                shape: BoxShape.circle,
                              ),
                            ),
                          );
                        },
                      ),
                      SizedBox(width: 8),
                      Text(
                        'LIVE',
                        style: GoogleFonts.poppins(
                          color: Colors.white,
                          fontSize: 12,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            
            if (widget.ride.isSharedRide) ...[
              SizedBox(height: 12),
              Container(
                padding: EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: AppTheme.accentColor.withOpacity(0.9),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Row(
                  children: [
                    Icon(Icons.psychology, color: Colors.white, size: 20),
                    SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        'AI Smart Match • ${widget.ride.sharedRiders?.length ?? 0} riders sharing',
                        style: GoogleFonts.poppins(
                          color: Colors.white,
                          fontSize: 12,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildBottomOverlay() {
    return Positioned(
      bottom: 0,
      left: 0,
      right: 0,
      child: Container(
        padding: EdgeInsets.all(20),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.1),
              blurRadius: 10,
              offset: Offset(0, -2),
            ),
          ],
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Driver info
            Row(
              children: [
                CircleAvatar(
                  radius: 25,
                  backgroundImage: NetworkImage(widget.ride.driver.photoUrl),
                ),
                SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        widget.ride.driver.name,
                        style: GoogleFonts.poppins(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Row(
                        children: [
                          Icon(Icons.star, color: Colors.amber, size: 16),
                          SizedBox(width: 4),
                          Text(
                            widget.ride.driver.ratingText,
                            style: GoogleFonts.poppins(fontSize: 14),
                          ),
                          SizedBox(width: 12),
                          Text(
                            widget.ride.driver.vehicleInfo,
                            style: GoogleFonts.poppins(
                              fontSize: 14,
                              color: AppTheme.textSecondary,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
                Column(
                  children: [
                    IconButton(
                      onPressed: () {
                        // Call driver
                        ScaffoldMessenger.of(context).showSnackBar(
                          SnackBar(content: Text('Calling ${widget.ride.driver.name}...')),
                        );
                      },
                      icon: Icon(Icons.phone, color: AppTheme.primaryColor),
                      style: IconButton.styleFrom(
                        backgroundColor: AppTheme.primaryColor.withOpacity(0.1),
                      ),
                    ),
                    Text(
                      'Call',
                      style: GoogleFonts.poppins(
                        fontSize: 10,
                        color: AppTheme.textSecondary,
                      ),
                    ),
                  ],
                ),
              ],
            ),
            
            SizedBox(height: 16),
            
            // Real-time stats
            if (_currentDriverLocation != null) ...[
              Container(
                padding: EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: AppTheme.backgroundColor,
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceAround,
                  children: [
                    _buildStatItem(
                      'Speed',
                      _currentDriverLocation!.speedText,
                      Icons.speed,
                    ),
                    _buildStatItem(
                      'ETA',
                      '${widget.ride.duration} min',
                      Icons.access_time,
                    ),
                    _buildStatItem(
                      'Distance',
                      '${widget.ride.distance.toStringAsFixed(1)} km',
                      Icons.straighten,
                    ),
                  ],
                ),
              ),
              SizedBox(height: 16),
            ],
            
            // Action buttons
            Row(
              children: [
                Expanded(
                  child: OutlinedButton(
                    onPressed: () {
                      _showRideDetails();
                    },
                    style: OutlinedButton.styleFrom(
                      side: BorderSide(color: AppTheme.primaryColor),
                      padding: EdgeInsets.symmetric(vertical: 12),
                    ),
                    child: Text(
                      'Trip Details',
                      style: GoogleFonts.poppins(
                        fontWeight: FontWeight.w600,
                        color: AppTheme.primaryColor,
                      ),
                    ),
                  ),
                ),
                SizedBox(width: 12),
                Expanded(
                  child: ElevatedButton(
                    onPressed: () {
                      _showSafetyOptions();
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppTheme.errorColor,
                      padding: EdgeInsets.symmetric(vertical: 12),
                    ),
                    child: Text(
                      'Safety',
                      style: GoogleFonts.poppins(
                        fontWeight: FontWeight.w600,
                        color: Colors.white,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatItem(String label, String value, IconData icon) {
    return Column(
      children: [
        Icon(icon, color: AppTheme.primaryColor, size: 20),
        SizedBox(height: 4),
        Text(
          value,
          style: GoogleFonts.poppins(
            fontSize: 14,
            fontWeight: FontWeight.bold,
            color: AppTheme.textPrimary,
          ),
        ),
        Text(
          label,
          style: GoogleFonts.poppins(
            fontSize: 10,
            color: AppTheme.textSecondary,
          ),
        ),
      ],
    );
  }

  Widget _buildFloatingButtons() {
    return Positioned(
      right: 20,
      top: MediaQuery.of(context).padding.top + 120,
      child: Column(
        children: [
          FloatingActionButton(
            mini: true,
            onPressed: () {
              setState(() {
                _isFollowingDriver = true;
              });
              if (_currentDriverLocation != null) {
                _followDriver(_currentDriverLocation!);
              }
            },
            backgroundColor: _isFollowingDriver ? AppTheme.primaryColor : Colors.white,
            child: Icon(
              Icons.my_location,
              color: _isFollowingDriver ? Colors.white : AppTheme.primaryColor,
            ),
          ),
          SizedBox(height: 12),
          FloatingActionButton(
            mini: true,
            onPressed: _fitMapToRoute,
            backgroundColor: Colors.white,
            child: Icon(Icons.zoom_out_map, color: AppTheme.primaryColor),
          ),
        ],
      ),
    );
  }

  void _showRideDetails() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => Container(
        padding: EdgeInsets.all(20),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Trip Details',
              style: GoogleFonts.poppins(
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(height: 20),
            Text('Pickup: ${widget.ride.pickupLocation.address}'),
            SizedBox(height: 8),
            Text('Destination: ${widget.ride.dropoffLocation.address}'),
            SizedBox(height: 8),
            Text('Fare: \$${widget.ride.fare.toStringAsFixed(2)}'),
            if (widget.ride.isSharedRide) ...[
              SizedBox(height: 8),
              Text('Shared with: ${widget.ride.sharedRiders?.join(', ') ?? 'None'}'),
            ],
            SizedBox(height: 20),
            ElevatedButton(
              onPressed: () => Navigator.pop(context),
              child: Text('Close'),
            ),
          ],
        ),
      ),
    );
  }

  void _showSafetyOptions() {
    showModalBottomSheet(
      context: context,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => Container(
        padding: EdgeInsets.all(20),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              'Safety Options',
              style: GoogleFonts.poppins(
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(height: 20),
            ListTile(
              leading: Icon(Icons.emergency, color: AppTheme.errorColor),
              title: Text('Emergency'),
              subtitle: Text('Call emergency services'),
              onTap: () {
                Navigator.pop(context);
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(content: Text('Emergency services contacted')),
                );
              },
            ),
            ListTile(
              leading: Icon(Icons.share_location, color: AppTheme.primaryColor),
              title: Text('Share Trip'),
              subtitle: Text('Share your location with contacts'),
              onTap: () {
                Navigator.pop(context);
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(content: Text('Trip shared with emergency contacts')),
                );
              },
            ),
            ListTile(
              leading: Icon(Icons.report, color: AppTheme.warningColor),
              title: Text('Report Issue'),
              subtitle: Text('Report safety concerns'),
              onTap: () {
                Navigator.pop(context);
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(content: Text('Safety report submitted')),
                );
              },
            ),
          ],
        ),
      ),
    );
  }
}
