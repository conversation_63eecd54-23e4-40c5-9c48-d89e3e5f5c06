import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:google_fonts/google_fonts.dart';
import '../../providers/ride_provider.dart';
import '../../providers/payment_provider.dart';
import '../../models/location_model.dart';
import '../../utils/app_theme.dart';
import 'payment_screen.dart';

class RideBookingScreen extends StatefulWidget {
  final String rideType;

  RideBookingScreen({required this.rideType});

  @override
  _RideBookingScreenState createState() => _RideBookingScreenState();
}

class _RideBookingScreenState extends State<RideBookingScreen> with TickerProviderStateMixin {
  final _pickupController = TextEditingController();
  final _dropoffController = TextEditingController();
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: Duration(milliseconds: 1000),
      vsync: this,
    );
    
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
    
    _slideAnimation = Tween<Offset>(
      begin: Offset(0, 0.3),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOutCubic,
    ));
    
    _animationController.forward();
    
    // Set ride type in provider
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<RideProvider>().setRideType(widget.rideType);
    });
  }

  @override
  void dispose() {
    _animationController.dispose();
    _pickupController.dispose();
    _dropoffController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      appBar: AppBar(
        title: Text(_getRideTypeTitle()),
        backgroundColor: Colors.transparent,
        elevation: 0,
      ),
      body: FadeTransition(
        opacity: _fadeAnimation,
        child: SlideTransition(
          position: _slideAnimation,
          child: SingleChildScrollView(
            padding: EdgeInsets.all(20),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                _buildRideTypeHeader(),
                SizedBox(height: 30),
                _buildLocationInputs(),
                SizedBox(height: 30),
                _buildRideOptions(),
                SizedBox(height: 30),
                _buildSearchButton(),
                SizedBox(height: 20),
                _buildRideSearchResults(),
              ],
            ),
          ),
        ),
      ),
    );
  }

  String _getRideTypeTitle() {
    switch (widget.rideType) {
      case 'individual':
        return 'Book Individual Ride';
      case 'shared':
        return 'Smart Ride Sharing';
      case 'package':
        return 'Backidge Delivery';
      default:
        return 'Book Ride';
    }
  }

  Widget _buildRideTypeHeader() {
    IconData icon;
    Color color;
    String description;

    switch (widget.rideType) {
      case 'individual':
        icon = Icons.directions_car;
        color = AppTheme.primaryColor;
        description = 'Book a private ride just for you';
        break;
      case 'shared':
        icon = Icons.group;
        color = AppTheme.successColor;
        description = 'AI-powered ride sharing to save money and reduce traffic';
        break;
      case 'package':
        icon = Icons.backpack;
        color = AppTheme.warningColor;
        description = 'Send packages quickly and securely';
        break;
      default:
        icon = Icons.directions_car;
        color = AppTheme.primaryColor;
        description = 'Book your ride';
    }

    return Container(
      padding: EdgeInsets.all(24),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [color, color.withOpacity(0.7)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(20),
        boxShadow: AppTheme.cardShadow,
      ),
      child: Column(
        children: [
          Container(
            padding: EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.2),
              shape: BoxShape.circle,
            ),
            child: Icon(icon, size: 40, color: Colors.white),
          ),
          SizedBox(height: 16),
          Text(
            _getRideTypeTitle(),
            style: GoogleFonts.poppins(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
            textAlign: TextAlign.center,
          ),
          SizedBox(height: 8),
          Text(
            description,
            style: GoogleFonts.poppins(
              fontSize: 14,
              color: Colors.white.withOpacity(0.9),
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildLocationInputs() {
    return Container(
      padding: EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: AppTheme.cardShadow,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Where to?',
            style: GoogleFonts.poppins(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: AppTheme.textPrimary,
            ),
          ),
          SizedBox(height: 20),
          
          // Pickup Location
          Container(
            decoration: BoxDecoration(
              border: Border.all(color: Colors.grey.shade300),
              borderRadius: BorderRadius.circular(12),
            ),
            child: TextField(
              controller: _pickupController,
              decoration: InputDecoration(
                prefixIcon: Container(
                  margin: EdgeInsets.all(12),
                  width: 8,
                  height: 8,
                  decoration: BoxDecoration(
                    color: AppTheme.successColor,
                    shape: BoxShape.circle,
                  ),
                ),
                hintText: 'Pickup location',
                border: InputBorder.none,
                contentPadding: EdgeInsets.symmetric(horizontal: 16, vertical: 16),
              ),
              onTap: () {
                _showLocationPicker(true);
              },
              readOnly: true,
            ),
          ),
          
          SizedBox(height: 16),
          
          // Dropoff Location
          Container(
            decoration: BoxDecoration(
              border: Border.all(color: Colors.grey.shade300),
              borderRadius: BorderRadius.circular(12),
            ),
            child: TextField(
              controller: _dropoffController,
              decoration: InputDecoration(
                prefixIcon: Container(
                  margin: EdgeInsets.all(12),
                  width: 8,
                  height: 8,
                  decoration: BoxDecoration(
                    color: AppTheme.errorColor,
                    shape: BoxShape.circle,
                  ),
                ),
                hintText: widget.rideType == 'package' ? 'Delivery address' : 'Destination',
                border: InputBorder.none,
                contentPadding: EdgeInsets.symmetric(horizontal: 16, vertical: 16),
              ),
              onTap: () {
                _showLocationPicker(false);
              },
              readOnly: true,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildRideOptions() {
    if (widget.rideType != 'shared') return SizedBox.shrink();

    return Container(
      padding: EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: AppTheme.cardShadow,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.psychology, color: AppTheme.accentColor),
              SizedBox(width: 8),
              Text(
                'AI Smart Matching',
                style: GoogleFonts.poppins(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: AppTheme.textPrimary,
                ),
              ),
            ],
          ),
          SizedBox(height: 16),
          Container(
            padding: EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: AppTheme.accentColor.withOpacity(0.1),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Column(
              children: [
                Row(
                  children: [
                    Icon(Icons.savings, color: AppTheme.successColor, size: 20),
                    SizedBox(width: 8),
                    Text(
                      'Save up to 30% on ride costs',
                      style: GoogleFonts.poppins(
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
                SizedBox(height: 8),
                Row(
                  children: [
                    Icon(Icons.eco, color: AppTheme.successColor, size: 20),
                    SizedBox(width: 8),
                    Text(
                      'Reduce carbon footprint',
                      style: GoogleFonts.poppins(
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
                SizedBox(height: 8),
                Row(
                  children: [
                    Icon(Icons.people, color: AppTheme.successColor, size: 20),
                    SizedBox(width: 8),
                    Text(
                      'Meet like-minded travelers',
                      style: GoogleFonts.poppins(
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSearchButton() {
    return Consumer<RideProvider>(
      builder: (context, rideProvider, child) {
        return Container(
          height: 56,
          child: ElevatedButton(
            onPressed: rideProvider.isLoading ? null : _searchForRide,
            style: ElevatedButton.styleFrom(
              backgroundColor: AppTheme.primaryColor,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(16),
              ),
            ),
            child: rideProvider.isLoading
                ? Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      SizedBox(
                        width: 20,
                        height: 20,
                        child: CircularProgressIndicator(
                          color: Colors.white,
                          strokeWidth: 2,
                        ),
                      ),
                      SizedBox(width: 12),
                      Text(
                        'Searching...',
                        style: GoogleFonts.poppins(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                          color: Colors.white,
                        ),
                      ),
                    ],
                  )
                : Text(
                    widget.rideType == 'shared' ? 'Find Smart Match' : 'Search Rides',
                    style: GoogleFonts.poppins(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: Colors.white,
                    ),
                  ),
          ),
        );
      },
    );
  }

  Widget _buildRideSearchResults() {
    return Consumer<RideProvider>(
      builder: (context, rideProvider, child) {
        final currentRide = rideProvider.currentRide;
        
        if (currentRide == null) return SizedBox.shrink();

        return Container(
          padding: EdgeInsets.all(20),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(16),
            boxShadow: AppTheme.cardShadow,
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Icon(Icons.check_circle, color: AppTheme.successColor),
                  SizedBox(width: 8),
                  Text(
                    'Ride Found!',
                    style: GoogleFonts.poppins(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: AppTheme.textPrimary,
                    ),
                  ),
                ],
              ),
              SizedBox(height: 16),
              
              // Driver Info
              Row(
                children: [
                  CircleAvatar(
                    radius: 25,
                    backgroundImage: NetworkImage(currentRide.driver.photoUrl),
                  ),
                  SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          currentRide.driver.name,
                          style: GoogleFonts.poppins(
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        Row(
                          children: [
                            Icon(Icons.star, color: Colors.amber, size: 16),
                            SizedBox(width: 4),
                            Text(
                              currentRide.driver.ratingText,
                              style: GoogleFonts.poppins(fontSize: 14),
                            ),
                            SizedBox(width: 12),
                            Text(
                              currentRide.driver.vehicleInfo,
                              style: GoogleFonts.poppins(
                                fontSize: 14,
                                color: AppTheme.textSecondary,
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ],
              ),
              
              SizedBox(height: 16),
              
              // Ride Details
              Container(
                padding: EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: AppTheme.backgroundColor,
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Column(
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text('Distance:', style: GoogleFonts.poppins()),
                        Text('${currentRide.distance.toStringAsFixed(1)} km', 
                             style: GoogleFonts.poppins(fontWeight: FontWeight.w600)),
                      ],
                    ),
                    SizedBox(height: 8),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text('Duration:', style: GoogleFonts.poppins()),
                        Text('${currentRide.duration} min', 
                             style: GoogleFonts.poppins(fontWeight: FontWeight.w600)),
                      ],
                    ),
                    SizedBox(height: 8),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text('Fare:', style: GoogleFonts.poppins()),
                        Text('\$${currentRide.fare.toStringAsFixed(2)}', 
                             style: GoogleFonts.poppins(
                               fontWeight: FontWeight.bold,
                               color: AppTheme.primaryColor,
                               fontSize: 18,
                             )),
                      ],
                    ),
                  ],
                ),
              ),
              
              SizedBox(height: 20),
              
              // Confirm Button
              Container(
                width: double.infinity,
                height: 50,
                child: ElevatedButton(
                  onPressed: () {
                    _confirmRide(currentRide);
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppTheme.successColor,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                  child: Text(
                    'Confirm & Pay',
                    style: GoogleFonts.poppins(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: Colors.white,
                    ),
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  void _showLocationPicker(bool isPickup) {
    // Mock location picker - in real app, integrate with Google Places API
    showModalBottomSheet(
      context: context,
      builder: (context) => Container(
        padding: EdgeInsets.all(20),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              'Select ${isPickup ? 'Pickup' : 'Destination'} Location',
              style: GoogleFonts.poppins(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(height: 20),
            ListTile(
              leading: Icon(Icons.location_on),
              title: Text('Current Location'),
              onTap: () {
                if (isPickup) {
                  _pickupController.text = 'Current Location';
                } else {
                  _dropoffController.text = 'Selected Destination';
                }
                Navigator.pop(context);
              },
            ),
            ListTile(
              leading: Icon(Icons.home),
              title: Text('Home'),
              subtitle: Text('123 Main St, Downtown'),
              onTap: () {
                if (isPickup) {
                  _pickupController.text = '123 Main St, Downtown';
                } else {
                  _dropoffController.text = '123 Main St, Downtown';
                }
                Navigator.pop(context);
              },
            ),
            ListTile(
              leading: Icon(Icons.work),
              title: Text('Work'),
              subtitle: Text('456 Business Ave, Uptown'),
              onTap: () {
                if (isPickup) {
                  _pickupController.text = '456 Business Ave, Uptown';
                } else {
                  _dropoffController.text = '456 Business Ave, Uptown';
                }
                Navigator.pop(context);
              },
            ),
          ],
        ),
      ),
    );
  }

  void _searchForRide() async {
    if (_pickupController.text.isEmpty || _dropoffController.text.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Please select pickup and destination locations')),
      );
      return;
    }

    // Create mock locations
    LocationModel pickup = LocationModel(
      address: _pickupController.text,
      latitude: 40.7128,
      longitude: -74.0060,
    );

    LocationModel dropoff = LocationModel(
      address: _dropoffController.text,
      latitude: 40.7589,
      longitude: -73.9851,
    );

    await context.read<RideProvider>().searchForRide(pickup, dropoff);
  }

  void _confirmRide(ride) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => PaymentScreen(ride: ride),
      ),
    );
  }
}
